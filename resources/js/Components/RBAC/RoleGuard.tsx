import React from 'react';
import useTypedPage from '@/Hooks/useTypedPage';

interface RoleGuardProps {
  roles: string | string[];
  fallback?: React.ReactNode;
  children: React.ReactNode;
  requireAll?: boolean; // If true, user must have ALL roles. If false, user needs ANY role.
}

/**
 * Component that conditionally renders children based on user roles
 */
export default function RoleGuard({
  roles,
  fallback = null,
  children,
  requireAll = false,
}: RoleGuardProps) {
  const page = useTypedPage();
  const user = page.props.auth.user;

  if (!user) {
    return <>{fallback}</>;
  }

  const userRole = user.role;
  const allowedRoles = Array.isArray(roles) ? roles : [roles];

  const hasRole = requireAll
    ? allowedRoles.every(role => userRole === role) // This doesn't make sense for single role, but kept for consistency
    : allowedRoles.includes(userRole);

  if (!hasRole) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

// Convenience components for specific roles
export function AdminGuard({ children, fallback = null }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleGuard roles="admin" fallback={fallback}>
      {children}
    </RoleGuard>
  );
}

export function ArtistGuard({ children, fallback = null }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleGuard roles="artist" fallback={fallback}>
      {children}
    </RoleGuard>
  );
}

export function ListenerGuard({ children, fallback = null }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleGuard roles="listener" fallback={fallback}>
      {children}
    </RoleGuard>
  );
}

export function AuthenticatedGuard({ children, fallback = null }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleGuard roles={['admin', 'artist', 'listener']} fallback={fallback}>
      {children}
    </RoleGuard>
  );
}
