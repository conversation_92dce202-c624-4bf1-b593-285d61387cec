import React, { useState } from 'react';
import { Search, Plus, Edit, Trash2, Key, Filter } from 'lucide-react';
import { But<PERSON> } from '@/Components/ui/button';
import { Badge } from '@/Components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/Components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/Components/ui/dialog';

interface Permission {
  id: number;
  name: string;
  display_name: string;
  category: string;
  description: string;
  is_active: boolean;
}

interface PermissionManagerProps {
  permissions: Permission[];
  onPermissionUpdate?: (permission: Permission) => void;
  onPermissionDelete?: (permissionId: number) => void;
  onPermissionCreate?: (permission: Partial<Permission>) => void;
}

export default function PermissionManager({ 
  permissions, 
  onPermissionUpdate, 
  onPermissionDelete, 
  onPermissionCreate 
}: PermissionManagerProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedPermission, setSelectedPermission] = useState<Permission | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  // Get unique categories
  const categories = ['all', ...Array.from(new Set(permissions.map(p => p.category)))];

  // Filter permissions
  const filteredPermissions = permissions.filter(permission => {
    const matchesSearch = permission.display_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         permission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         permission.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = selectedCategory === 'all' || permission.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  // Group permissions by category
  const groupedPermissions = filteredPermissions.reduce((acc, permission) => {
    if (!acc[permission.category]) {
      acc[permission.category] = [];
    }
    acc[permission.category].push(permission);
    return acc;
  }, {} as Record<string, Permission[]>);

  const getCategoryColor = (category: string) => {
    const colors = {
      'user_management': 'bg-blue-100 text-blue-800',
      'music': 'bg-green-100 text-green-800',
      'artist': 'bg-purple-100 text-purple-800',
      'content': 'bg-yellow-100 text-yellow-800',
      'system': 'bg-red-100 text-red-800',
      'listener': 'bg-indigo-100 text-indigo-800',
    };
    return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Permission Management</h3>
          <p className="text-sm text-muted-foreground">
            Manage system permissions and their categories
          </p>
        </div>
        
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Permission
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Permission</DialogTitle>
            </DialogHeader>
            <div className="p-4">
              <p className="text-sm text-muted-foreground">
                Permission creation form will be implemented here
              </p>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <input
            type="text"
            placeholder="Search permissions..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-4 py-2 w-full border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
          />
        </div>
        
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-muted-foreground" />
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
          >
            {categories.map(category => (
              <option key={category} value={category}>
                {category === 'all' ? 'All Categories' : category.replace('_', ' ').toUpperCase()}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Permissions Grid */}
      <div className="space-y-6">
        {Object.entries(groupedPermissions).map(([category, categoryPermissions]) => (
          <Card key={category}>
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <Key className="h-5 w-5" />
                <span className="capitalize">{category.replace('_', ' ')}</span>
                <Badge variant="secondary">{categoryPermissions.length}</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {categoryPermissions.map((permission) => (
                  <div
                    key={permission.id}
                    className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <h4 className="font-medium text-sm">{permission.display_name}</h4>
                        <p className="text-xs text-muted-foreground mt-1">
                          {permission.name}
                        </p>
                      </div>
                      
                      <div className="flex items-center gap-1">
                        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
                          <DialogTrigger asChild>
                            <Button 
                              variant="ghost" 
                              size="icon"
                              className="h-6 w-6"
                              onClick={() => setSelectedPermission(permission)}
                            >
                              <Edit className="h-3 w-3" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>Edit Permission: {selectedPermission?.display_name}</DialogTitle>
                            </DialogHeader>
                            {selectedPermission && (
                              <div className="space-y-4">
                                <div>
                                  <label className="text-sm font-medium">Permission Name</label>
                                  <p className="text-sm text-muted-foreground">{selectedPermission.name}</p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">Display Name</label>
                                  <p className="text-sm text-muted-foreground">{selectedPermission.display_name}</p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">Category</label>
                                  <Badge className={getCategoryColor(selectedPermission.category)}>
                                    {selectedPermission.category}
                                  </Badge>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">Description</label>
                                  <p className="text-sm text-muted-foreground">{selectedPermission.description}</p>
                                </div>
                              </div>
                            )}
                          </DialogContent>
                        </Dialog>
                        
                        <Button 
                          variant="ghost" 
                          size="icon"
                          className="h-6 w-6"
                          onClick={() => onPermissionDelete?.(permission.id)}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    
                    <p className="text-xs text-muted-foreground mb-3">
                      {permission.description}
                    </p>
                    
                    <div className="flex items-center justify-between">
                      <Badge className={getCategoryColor(permission.category)}>
                        {permission.category}
                      </Badge>
                      
                      <Badge variant={permission.is_active ? 'success' : 'secondary'}>
                        {permission.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredPermissions.length === 0 && (
        <div className="text-center py-12">
          <Key className="h-16 w-16 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
            No permissions found
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            Try adjusting your search or filter criteria.
          </p>
        </div>
      )}
    </div>
  );
}
