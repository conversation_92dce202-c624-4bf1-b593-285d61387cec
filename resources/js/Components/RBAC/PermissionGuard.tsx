import React from 'react';
import useTypedPage from '@/Hooks/useTypedPage';

interface PermissionGuardProps {
  permission: string | string[];
  fallback?: React.ReactNode;
  children: React.ReactNode;
  requireAll?: boolean; // If true, user must have ALL permissions. If false, user needs ANY permission.
}

/**
 * Component that conditionally renders children based on user permissions
 */
export default function PermissionGuard({
  permission,
  fallback = null,
  children,
  requireAll = false,
}: PermissionGuardProps) {
  const page = useTypedPage();
  const user = page.props.auth.user;

  if (!user) {
    return <>{fallback}</>;
  }

  // Get user permissions from the page props or user object
  const userPermissions = user.permissions || [];
  const permissionNames = userPermissions.map((p: any) => p.name || p);

  const permissions = Array.isArray(permission) ? permission : [permission];

  const hasPermission = requireAll
    ? permissions.every(p => permissionNames.includes(p))
    : permissions.some(p => permissionNames.includes(p));

  if (!hasPermission) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

// Convenience components for common permission checks
export function AdminOnly({ children, fallback = null }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <PermissionGuard permission="admin_access" fallback={fallback}>
      {children}
    </PermissionGuard>
  );
}

export function ArtistOnly({ children, fallback = null }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <PermissionGuard permission="artist_access" fallback={fallback}>
      {children}
    </PermissionGuard>
  );
}

export function ListenerOnly({ children, fallback = null }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <PermissionGuard permission="listener_access" fallback={fallback}>
      {children}
    </PermissionGuard>
  );
}
