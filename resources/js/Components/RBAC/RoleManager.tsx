import React, { useState } from 'react';
import { Plus, Edit, Trash2, Users, <PERSON>, Settings } from 'lucide-react';
import { But<PERSON> } from '@/Components/ui/button';
import { Badge } from '@/Components/ui/badge';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/Components/ui/card';
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/Components/ui/dialog';

interface Role {
  id: number;
  name: string;
  display_name: string;
  description: string;
  color: string;
  level: number;
  users_count: number;
  permissions: Permission[];
  is_active: boolean;
}

interface Permission {
  id: number;
  name: string;
  display_name: string;
  category: string;
  description: string;
}

interface RoleManagerProps {
  roles: Role[];
  permissions: Permission[];
  onRoleUpdate?: (role: Role) => void;
  onRoleDelete?: (roleId: number) => void;
  onRoleCreate?: (role: Partial<Role>) => void;
}

export default function RoleManager({ 
  roles, 
  permissions, 
  onRoleUpdate, 
  onRoleDelete, 
  onRoleCreate 
}: RoleManagerProps) {
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  const getLevelName = (level: number) => {
    const levels = {
      0: 'Basic',
      1: 'Standard', 
      2: 'Advanced',
      3: 'Manager',
      4: 'Administrator',
      5: 'Super Admin'
    };
    return levels[level as keyof typeof levels] || 'Unknown';
  };

  const getPermissionsByCategory = (rolePermissions: Permission[]) => {
    return rolePermissions.reduce((acc, permission) => {
      if (!acc[permission.category]) {
        acc[permission.category] = [];
      }
      acc[permission.category].push(permission);
      return acc;
    }, {} as Record<string, Permission[]>);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Role Management</h2>
          <p className="text-muted-foreground">
            Manage user roles and permissions for your application
          </p>
        </div>
        
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Role
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Role</DialogTitle>
            </DialogHeader>
            {/* Role creation form would go here */}
            <div className="p-4">
              <p className="text-sm text-muted-foreground">
                Role creation form will be implemented here
              </p>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Roles Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {roles.map((role) => (
          <Card key={role.id} className="relative">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div 
                    className="w-4 h-4 rounded-full"
                    style={{ backgroundColor: role.color }}
                  />
                  <CardTitle className="text-lg">{role.display_name}</CardTitle>
                </div>
                
                <div className="flex items-center gap-1">
                  <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
                    <DialogTrigger asChild>
                      <Button 
                        variant="ghost" 
                        size="icon"
                        onClick={() => setSelectedRole(role)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-2xl">
                      <DialogHeader>
                        <DialogTitle>Edit Role: {selectedRole?.display_name}</DialogTitle>
                      </DialogHeader>
                      {selectedRole && (
                        <div className="space-y-4">
                          {/* Role details */}
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <label className="text-sm font-medium">Role Name</label>
                              <p className="text-sm text-muted-foreground">{selectedRole.name}</p>
                            </div>
                            <div>
                              <label className="text-sm font-medium">Level</label>
                              <p className="text-sm text-muted-foreground">
                                {selectedRole.level} - {getLevelName(selectedRole.level)}
                              </p>
                            </div>
                          </div>
                          
                          <div>
                            <label className="text-sm font-medium">Description</label>
                            <p className="text-sm text-muted-foreground">{selectedRole.description}</p>
                          </div>

                          {/* Permissions */}
                          <div>
                            <label className="text-sm font-medium mb-3 block">Permissions</label>
                            <div className="space-y-3">
                              {Object.entries(getPermissionsByCategory(selectedRole.permissions)).map(([category, perms]) => (
                                <div key={category}>
                                  <h4 className="text-sm font-medium capitalize mb-2">{category}</h4>
                                  <div className="flex flex-wrap gap-2">
                                    {perms.map((permission) => (
                                      <Badge key={permission.id} variant="secondary">
                                        {permission.display_name}
                                      </Badge>
                                    ))}
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      )}
                    </DialogContent>
                  </Dialog>
                  
                  <Button 
                    variant="ghost" 
                    size="icon"
                    onClick={() => onRoleDelete?.(role.id)}
                    disabled={role.users_count > 0}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
              <p className="text-sm text-muted-foreground">{role.description}</p>
            </CardHeader>
            
            <CardContent>
              <div className="space-y-4">
                {/* Role Stats */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{role.users_count} users</span>
                  </div>
                  
                  <Badge variant={role.is_active ? 'success' : 'secondary'}>
                    {role.is_active ? 'Active' : 'Inactive'}
                  </Badge>
                </div>
                
                {/* Level Badge */}
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4 text-muted-foreground" />
                  <Badge variant="outline">
                    Level {role.level} - {getLevelName(role.level)}
                  </Badge>
                </div>
                
                {/* Permissions Count */}
                <div className="flex items-center gap-2">
                  <Settings className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{role.permissions.length} permissions</span>
                </div>
                
                {/* Permission Categories */}
                <div>
                  <p className="text-xs text-muted-foreground mb-2">Permission Categories:</p>
                  <div className="flex flex-wrap gap-1">
                    {Object.keys(getPermissionsByCategory(role.permissions)).map((category) => (
                      <Badge key={category} variant="outline" className="text-xs">
                        {category}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
