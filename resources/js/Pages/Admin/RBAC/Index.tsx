import React, { useState } from 'react';
import { Head } from '@inertiajs/react';
import WithLayout from '@/Components/WithLayout';
import { But<PERSON> } from '@/Components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/Components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/Components/ui/tabs';
import RoleManager from '@/Components/RBAC/RoleManager';
import PermissionManager from '@/Components/RBAC/PermissionManager';
import UserRoleManager from '@/Components/RBAC/UserRoleManager';
import { AdminGuard } from '@/Components/RBAC/RoleGuard';
import { Shield, Users, Key, Settings, BarChart3 } from 'lucide-react';

interface Role {
  id: number;
  name: string;
  display_name: string;
  description: string;
  color: string;
  level: number;
  users_count: number;
  permissions: Permission[];
  is_active: boolean;
}

interface Permission {
  id: number;
  name: string;
  display_name: string;
  category: string;
  description: string;
}

interface User {
  id: number;
  name: string;
  email: string;
  role: string;
  role_id: number;
  is_active: boolean;
  last_login_at: string;
  permissions: Permission[];
}

interface RBACIndexProps {
  roles: Role[];
  permissions: Permission[];
  users: User[];
  stats: {
    total_roles: number;
    total_permissions: number;
    total_users: number;
    active_users: number;
  };
}

export default function RBACIndex({ roles, permissions, users, stats }: RBACIndexProps) {
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <WithLayout
      title="RBAC Management"
      renderHeader={() => (
        <div className="flex items-center justify-between">
          <div>
            <h2 className="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
              Role-Based Access Control
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Manage roles, permissions, and user access across your application
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Shield className="h-8 w-8 text-purple-600" />
          </div>
        </div>
      )}
    >
      <Head title="RBAC Management" />

      <AdminGuard fallback={
        <div className="text-center py-12">
          <Shield className="h-16 w-16 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
            Access Denied
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            You don't have permission to access RBAC management.
          </p>
        </div>
      }>
        <div className="space-y-6">
          {/* Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Roles</CardTitle>
                <Shield className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.total_roles}</div>
                <p className="text-xs text-muted-foreground">
                  System roles configured
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Permissions</CardTitle>
                <Key className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.total_permissions}</div>
                <p className="text-xs text-muted-foreground">
                  Available permissions
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.total_users}</div>
                <p className="text-xs text-muted-foreground">
                  Registered users
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Users</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.active_users}</div>
                <p className="text-xs text-muted-foreground">
                  Currently active
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Main Content Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview" className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4" />
                Overview
              </TabsTrigger>
              <TabsTrigger value="roles" className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Roles
              </TabsTrigger>
              <TabsTrigger value="permissions" className="flex items-center gap-2">
                <Key className="h-4 w-4" />
                Permissions
              </TabsTrigger>
              <TabsTrigger value="users" className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                User Roles
              </TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Role Distribution */}
                <Card>
                  <CardHeader>
                    <CardTitle>Role Distribution</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {roles.map((role) => (
                        <div key={role.id} className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div 
                              className="w-3 h-3 rounded-full"
                              style={{ backgroundColor: role.color }}
                            />
                            <span className="font-medium">{role.display_name}</span>
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {role.users_count} users
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Permission Categories */}
                <Card>
                  <CardHeader>
                    <CardTitle>Permission Categories</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {Object.entries(
                        permissions.reduce((acc, permission) => {
                          acc[permission.category] = (acc[permission.category] || 0) + 1;
                          return acc;
                        }, {} as Record<string, number>)
                      ).map(([category, count]) => (
                        <div key={category} className="flex items-center justify-between">
                          <span className="font-medium capitalize">{category.replace('_', ' ')}</span>
                          <span className="text-sm text-muted-foreground">{count} permissions</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="roles">
              <RoleManager 
                roles={roles} 
                permissions={permissions}
                onRoleUpdate={(role) => {
                  // Handle role update
                  console.log('Role updated:', role);
                }}
                onRoleDelete={(roleId) => {
                  // Handle role deletion
                  console.log('Role deleted:', roleId);
                }}
                onRoleCreate={(role) => {
                  // Handle role creation
                  console.log('Role created:', role);
                }}
              />
            </TabsContent>

            <TabsContent value="permissions">
              <PermissionManager permissions={permissions} />
            </TabsContent>

            <TabsContent value="users">
              <UserRoleManager users={users} roles={roles} />
            </TabsContent>
          </Tabs>
        </div>
      </AdminGuard>
    </WithLayout>
  );
}
