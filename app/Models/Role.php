<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Role extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'display_name',
        'description',
        'color',
        'level',
        'metadata',
        'is_active',
        'is_default',
    ];

    protected $casts = [
        'metadata' => 'array',
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'level' => 'integer',
    ];

    /**
     * Get the users that have this role.
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get the permissions for this role.
     */
    public function permissions(): BelongsToMany
    {
        return $this->belongsToMany(Permission::class, 'role_permissions')
            ->withPivot('conditions')
            ->withTimestamps();
    }

    /**
     * Scope to only active roles.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to roles by level.
     */
    public function scopeByLevel($query, int $level)
    {
        return $query->where('level', $level);
    }

    /**
     * Scope to roles with level greater than or equal to.
     */
    public function scopeMinLevel($query, int $level)
    {
        return $query->where('level', '>=', $level);
    }

    /**
     * Get the default role for new users.
     */
    public static function getDefault(): ?self
    {
        return static::where('is_default', true)->first();
    }

    /**
     * Check if this role has a specific permission.
     */
    public function hasPermission(string $permission): bool
    {
        return $this->permissions()
            ->where('name', $permission)
            ->where('is_active', true)
            ->exists();
    }

    /**
     * Grant a permission to this role.
     */
    public function grantPermission(string|Permission $permission, array $conditions = []): void
    {
        $permissionModel = is_string($permission) 
            ? Permission::where('name', $permission)->first()
            : $permission;

        if ($permissionModel && !$this->hasPermission($permissionModel->name)) {
            $this->permissions()->attach($permissionModel->id, [
                'conditions' => $conditions ? json_encode($conditions) : null,
            ]);
        }
    }

    /**
     * Revoke a permission from this role.
     */
    public function revokePermission(string|Permission $permission): void
    {
        $permissionModel = is_string($permission) 
            ? Permission::where('name', $permission)->first()
            : $permission;

        if ($permissionModel) {
            $this->permissions()->detach($permissionModel->id);
        }
    }

    /**
     * Sync permissions for this role.
     */
    public function syncPermissions(array $permissions): void
    {
        $permissionIds = Permission::whereIn('name', $permissions)->pluck('id');
        $this->permissions()->sync($permissionIds);
    }

    /**
     * Get role hierarchy level names.
     */
    public static function getLevelNames(): array
    {
        return [
            0 => 'Basic',
            1 => 'Standard',
            2 => 'Advanced',
            3 => 'Manager',
            4 => 'Administrator',
            5 => 'Super Admin',
        ];
    }

    /**
     * Get role level name.
     */
    public function getLevelName(): string
    {
        $levels = static::getLevelNames();
        return $levels[$this->level] ?? 'Unknown';
    }
}
