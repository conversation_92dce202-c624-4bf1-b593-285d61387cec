<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class NotificationSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'type',
        'email_enabled',
        'push_enabled',
        'in_app_enabled',
        'sms_enabled',
        'schedule',
        'conditions',
    ];

    protected $casts = [
        'email_enabled' => 'boolean',
        'push_enabled' => 'boolean',
        'in_app_enabled' => 'boolean',
        'sms_enabled' => 'boolean',
        'schedule' => 'array',
        'conditions' => 'array',
    ];

    /**
     * Get the user that owns the notification setting.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get default notification settings for a user.
     */
    public static function getDefaultSettings(): array
    {
        return [
            'music_upload' => [
                'email_enabled' => true,
                'push_enabled' => true,
                'in_app_enabled' => true,
                'sms_enabled' => false,
            ],
            'new_follower' => [
                'email_enabled' => true,
                'push_enabled' => true,
                'in_app_enabled' => true,
                'sms_enabled' => false,
            ],
            'track_liked' => [
                'email_enabled' => false,
                'push_enabled' => true,
                'in_app_enabled' => true,
                'sms_enabled' => false,
            ],
            'system_announcement' => [
                'email_enabled' => true,
                'push_enabled' => true,
                'in_app_enabled' => true,
                'sms_enabled' => false,
            ],
            'security_alert' => [
                'email_enabled' => true,
                'push_enabled' => true,
                'in_app_enabled' => true,
                'sms_enabled' => true,
            ],
        ];
    }

    /**
     * Create default notification settings for a user.
     */
    public static function createDefaultForUser(User $user): void
    {
        $defaultSettings = self::getDefaultSettings();

        foreach ($defaultSettings as $type => $settings) {
            self::firstOrCreate(
                ['user_id' => $user->id, 'type' => $type],
                $settings
            );
        }
    }

    /**
     * Check if notification type is enabled for a specific channel.
     */
    public function isEnabledFor(string $channel): bool
    {
        return match ($channel) {
            'email' => $this->email_enabled,
            'push' => $this->push_enabled,
            'in_app' => $this->in_app_enabled,
            'sms' => $this->sms_enabled,
            default => false,
        };
    }
}
