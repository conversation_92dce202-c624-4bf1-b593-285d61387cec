<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Permission extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'display_name',
        'description',
        'category',
        'metadata',
        'is_active',
    ];

    protected $casts = [
        'metadata' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get the roles that have this permission.
     */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, 'role_permissions')
            ->withPivot('conditions')
            ->withTimestamps();
    }

    /**
     * Get the users that have this permission directly.
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_permissions')
            ->withPivot(['type', 'conditions', 'expires_at', 'granted_by', 'reason'])
            ->withTimestamps();
    }

    /**
     * Scope to only active permissions.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to permissions by category.
     */
    public function scopeByCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Get all permissions grouped by category.
     */
    public static function getGroupedByCategory()
    {
        return static::active()
            ->orderBy('category')
            ->orderBy('display_name')
            ->get()
            ->groupBy('category');
    }

    /**
     * Check if permission exists by name.
     */
    public static function exists(string $name): bool
    {
        return static::where('name', $name)->exists();
    }

    /**
     * Create permission if it doesn't exist.
     */
    public static function createIfNotExists(array $attributes): self
    {
        return static::firstOrCreate(
            ['name' => $attributes['name']],
            $attributes
        );
    }
}
