<?php

namespace App\Traits;

use App\Models\Permission;
use App\Models\Role;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;

trait HasRoles
{
    /**
     * Get the user's role.
     */
    public function roleModel(): BelongsTo
    {
        return $this->belongsTo(Role::class, 'role_id');
    }

    /**
     * Get the user's direct permissions.
     */
    public function permissions(): BelongsToMany
    {
        return $this->belongsToMany(Permission::class, 'user_permissions')
            ->withPivot(['type', 'conditions', 'expires_at', 'granted_by', 'reason'])
            ->withTimestamps();
    }

    /**
     * Check if user has a specific role.
     */
    public function hasRole(string|array $roles): bool
    {
        if (is_array($roles)) {
            return in_array($this->role, $roles) ||
                   ($this->roleModel && in_array($this->roleModel->name, $roles));
        }

        return $this->role === $roles ||
               ($this->roleModel && $this->roleModel->name === $roles);
    }

    /**
     * Check if user has any of the given roles.
     */
    public function hasAnyRole(array $roles): bool
    {
        return $this->hasRole($roles);
    }

    /**
     * Check if user has all of the given roles.
     */
    public function hasAllRoles(array $roles): bool
    {
        foreach ($roles as $role) {
            if (!$this->hasRole($role)) {
                return false;
            }
        }
        return true;
    }

    /**
     * Assign a role to the user.
     */
    public function assignRole(string|Role $role): void
    {
        $roleModel = is_string($role) ? Role::where('name', $role)->first() : $role;

        if ($roleModel) {
            $this->update([
                'role' => $roleModel->name,
                'role_id' => $roleModel->id,
                'permissions_updated_at' => now(),
            ]);

            $this->clearPermissionsCache();
        }
    }

    /**
     * Check if user has a specific permission.
     */
    public function hasPermission(string $permission): bool
    {
        return $this->getAllPermissions()->contains('name', $permission);
    }

    /**
     * Check if user has any of the given permissions.
     */
    public function hasAnyPermission(array $permissions): bool
    {
        $userPermissions = $this->getAllPermissions()->pluck('name')->toArray();
        return !empty(array_intersect($permissions, $userPermissions));
    }

    /**
     * Check if user has all of the given permissions.
     */
    public function hasAllPermissions(array $permissions): bool
    {
        $userPermissions = $this->getAllPermissions()->pluck('name')->toArray();
        return empty(array_diff($permissions, $userPermissions));
    }

    /**
     * Get all permissions for the user (role + direct permissions).
     */
    public function getAllPermissions(): Collection
    {
        $cacheKey = "user_permissions_{$this->id}";

        return Cache::remember($cacheKey, 3600, function () {
            $permissions = collect();

            // Get permissions from role
            if ($this->roleModel) {
                $rolePermissions = $this->roleModel->permissions()
                    ->where('is_active', true)
                    ->get();
                $permissions = $permissions->merge($rolePermissions);
            }

            // Get direct permissions
            $directPermissions = $this->permissions()
                ->where('is_active', true)
                ->wherePivot('type', 'grant')
                ->where(function ($query) {
                    $query->whereNull('user_permissions.expires_at')
                          ->orWhere('user_permissions.expires_at', '>', now());
                })
                ->get();

            $permissions = $permissions->merge($directPermissions);

            // Remove denied permissions
            $deniedPermissions = $this->permissions()
                ->where('is_active', true)
                ->wherePivot('type', 'deny')
                ->where(function ($query) {
                    $query->whereNull('user_permissions.expires_at')
                          ->orWhere('user_permissions.expires_at', '>', now());
                })
                ->pluck('name')
                ->toArray();

            return $permissions->reject(function ($permission) use ($deniedPermissions) {
                return in_array($permission->name, $deniedPermissions);
            })->unique('id');
        });
    }

    /**
     * Grant a permission to the user.
     */
    public function grantPermission(
        string|Permission $permission,
        array $conditions = [],
        ?\DateTime $expiresAt = null,
        ?int $grantedBy = null,
        ?string $reason = null
    ): void {
        $permissionModel = is_string($permission)
            ? Permission::where('name', $permission)->first()
            : $permission;

        if ($permissionModel) {
            $this->permissions()->syncWithoutDetaching([
                $permissionModel->id => [
                    'type' => 'grant',
                    'conditions' => $conditions ? json_encode($conditions) : null,
                    'expires_at' => $expiresAt,
                    'granted_by' => $grantedBy,
                    'reason' => $reason,
                ]
            ]);

            $this->clearPermissionsCache();
        }
    }

    /**
     * Deny a permission to the user.
     */
    public function denyPermission(
        string|Permission $permission,
        array $conditions = [],
        ?\DateTime $expiresAt = null,
        ?int $grantedBy = null,
        ?string $reason = null
    ): void {
        $permissionModel = is_string($permission)
            ? Permission::where('name', $permission)->first()
            : $permission;

        if ($permissionModel) {
            $this->permissions()->syncWithoutDetaching([
                $permissionModel->id => [
                    'type' => 'deny',
                    'conditions' => $conditions ? json_encode($conditions) : null,
                    'expires_at' => $expiresAt,
                    'granted_by' => $grantedBy,
                    'reason' => $reason,
                ]
            ]);

            $this->clearPermissionsCache();
        }
    }

    /**
     * Revoke a permission from the user.
     */
    public function revokePermission(string|Permission $permission): void
    {
        $permissionModel = is_string($permission)
            ? Permission::where('name', $permission)->first()
            : $permission;

        if ($permissionModel) {
            $this->permissions()->detach($permissionModel->id);
            $this->clearPermissionsCache();
        }
    }

    /**
     * Clear the user's permissions cache.
     */
    public function clearPermissionsCache(): void
    {
        Cache::forget("user_permissions_{$this->id}");
        $this->update(['permissions_updated_at' => now()]);
    }

    /**
     * Check if user can perform action on resource.
     */
    public function canPerform(string $permission, $resource = null): bool
    {
        // Check basic permission
        if (!$this->hasPermission($permission)) {
            return false;
        }

        // Add resource-specific logic here if needed
        // For example, check if user owns the resource

        return true;
    }

    /**
     * Get user's role level.
     */
    public function getRoleLevel(): int
    {
        return $this->roleModel?->level ?? 0;
    }

    /**
     * Check if user has higher or equal role level.
     */
    public function hasMinRoleLevel(int $level): bool
    {
        return $this->getRoleLevel() >= $level;
    }
}
