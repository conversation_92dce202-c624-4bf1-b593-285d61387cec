<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    /**
     * Get user's notifications.
     */
    public function index(Request $request): JsonResponse
    {
        $user = Auth::user();
        
        $notifications = $user->notifications()
            ->when($request->unread_only, function ($query) {
                return $query->whereNull('read_at');
            })
            ->when($request->category, function ($query, $category) {
                return $query->where('data->category', $category);
            })
            ->orderBy('created_at', 'desc')
            ->paginate($request->per_page ?? 20);

        $unreadCount = $user->unreadNotifications()->count();

        return response()->json([
            'success' => true,
            'data' => $notifications->items(),
            'unread_count' => $unreadCount,
            'pagination' => [
                'current_page' => $notifications->currentPage(),
                'last_page' => $notifications->lastPage(),
                'per_page' => $notifications->perPage(),
                'total' => $notifications->total(),
            ],
        ]);
    }

    /**
     * Mark notification as read.
     */
    public function markAsRead(Request $request, string $id): JsonResponse
    {
        $user = Auth::user();
        
        $notification = $user->notifications()->find($id);
        
        if (!$notification) {
            return response()->json([
                'success' => false,
                'message' => 'Notification not found',
            ], 404);
        }

        $notification->markAsRead();

        return response()->json([
            'success' => true,
            'message' => 'Notification marked as read',
        ]);
    }

    /**
     * Mark all notifications as read.
     */
    public function markAllAsRead(): JsonResponse
    {
        $user = Auth::user();
        
        $user->unreadNotifications()->update(['read_at' => now()]);

        return response()->json([
            'success' => true,
            'message' => 'All notifications marked as read',
        ]);
    }

    /**
     * Delete notification.
     */
    public function destroy(string $id): JsonResponse
    {
        $user = Auth::user();
        
        $notification = $user->notifications()->find($id);
        
        if (!$notification) {
            return response()->json([
                'success' => false,
                'message' => 'Notification not found',
            ], 404);
        }

        $notification->delete();

        return response()->json([
            'success' => true,
            'message' => 'Notification deleted',
        ]);
    }

    /**
     * Get notification statistics.
     */
    public function stats(): JsonResponse
    {
        $user = Auth::user();
        
        $stats = [
            'total' => $user->notifications()->count(),
            'unread' => $user->unreadNotifications()->count(),
            'today' => $user->notifications()->whereDate('created_at', today())->count(),
            'this_week' => $user->notifications()->whereBetween('created_at', [
                now()->startOfWeek(),
                now()->endOfWeek()
            ])->count(),
        ];

        // Get notifications by category
        $byCategory = $user->notifications()
            ->selectRaw("JSON_UNQUOTE(JSON_EXTRACT(data, '$.category')) as category, COUNT(*) as count")
            ->groupBy('category')
            ->pluck('count', 'category')
            ->toArray();

        return response()->json([
            'success' => true,
            'data' => [
                'stats' => $stats,
                'by_category' => $byCategory,
            ],
        ]);
    }

    /**
     * Get notification settings.
     */
    public function getSettings(): JsonResponse
    {
        $user = Auth::user();
        
        $settings = $user->notificationSettings()
            ->get()
            ->keyBy('type');

        return response()->json([
            'success' => true,
            'data' => $settings,
        ]);
    }

    /**
     * Update notification settings.
     */
    public function updateSettings(Request $request): JsonResponse
    {
        $request->validate([
            'settings' => 'required|array',
            'settings.*.type' => 'required|string',
            'settings.*.email_enabled' => 'boolean',
            'settings.*.push_enabled' => 'boolean',
            'settings.*.in_app_enabled' => 'boolean',
            'settings.*.sms_enabled' => 'boolean',
        ]);

        $user = Auth::user();

        foreach ($request->settings as $settingData) {
            $user->notificationSettings()->updateOrCreate(
                ['type' => $settingData['type']],
                $settingData
            );
        }

        return response()->json([
            'success' => true,
            'message' => 'Notification settings updated',
        ]);
    }
}
