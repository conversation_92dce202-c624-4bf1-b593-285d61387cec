<?php

namespace Database\Seeders;

use App\Models\Permission;
use App\Models\Role;
use App\Models\User;
use Illuminate\Database\Seeder;

class RBACSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Create Permissions
        $permissions = [
            // User Management
            ['name' => 'view_users', 'display_name' => 'View Users', 'category' => 'user_management', 'description' => 'View user list and profiles'],
            ['name' => 'create_users', 'display_name' => 'Create Users', 'category' => 'user_management', 'description' => 'Create new user accounts'],
            ['name' => 'edit_users', 'display_name' => 'Edit Users', 'category' => 'user_management', 'description' => 'Edit user information'],
            ['name' => 'delete_users', 'display_name' => 'Delete Users', 'category' => 'user_management', 'description' => 'Delete user accounts'],
            ['name' => 'manage_user_roles', 'display_name' => 'Manage User Roles', 'category' => 'user_management', 'description' => 'Assign and modify user roles'],

            // Music Management
            ['name' => 'view_music', 'display_name' => 'View Music', 'category' => 'music', 'description' => 'View music tracks and albums'],
            ['name' => 'upload_music', 'display_name' => 'Upload Music', 'category' => 'music', 'description' => 'Upload new music tracks'],
            ['name' => 'edit_music', 'display_name' => 'Edit Music', 'category' => 'music', 'description' => 'Edit music information'],
            ['name' => 'delete_music', 'display_name' => 'Delete Music', 'category' => 'music', 'description' => 'Delete music tracks'],
            ['name' => 'manage_albums', 'display_name' => 'Manage Albums', 'category' => 'music', 'description' => 'Create and manage albums'],
            ['name' => 'manage_genres', 'display_name' => 'Manage Genres', 'category' => 'music', 'description' => 'Create and manage music genres'],

            // Artist Management
            ['name' => 'view_artists', 'display_name' => 'View Artists', 'category' => 'artist', 'description' => 'View artist profiles'],
            ['name' => 'manage_artist_profile', 'display_name' => 'Manage Artist Profile', 'category' => 'artist', 'description' => 'Manage own artist profile'],
            ['name' => 'view_artist_analytics', 'display_name' => 'View Artist Analytics', 'category' => 'artist', 'description' => 'View artist performance analytics'],

            // Content Management
            ['name' => 'view_blogs', 'display_name' => 'View Blogs', 'category' => 'content', 'description' => 'View blog posts'],
            ['name' => 'create_blogs', 'display_name' => 'Create Blogs', 'category' => 'content', 'description' => 'Create new blog posts'],
            ['name' => 'edit_blogs', 'display_name' => 'Edit Blogs', 'category' => 'content', 'description' => 'Edit blog posts'],
            ['name' => 'delete_blogs', 'display_name' => 'Delete Blogs', 'category' => 'content', 'description' => 'Delete blog posts'],
            ['name' => 'manage_comments', 'display_name' => 'Manage Comments', 'category' => 'content', 'description' => 'Moderate user comments'],

            // System Administration
            ['name' => 'admin_access', 'display_name' => 'Admin Access', 'category' => 'system', 'description' => 'Access admin dashboard'],
            ['name' => 'view_system_logs', 'display_name' => 'View System Logs', 'category' => 'system', 'description' => 'View system logs and activities'],
            ['name' => 'manage_settings', 'display_name' => 'Manage Settings', 'category' => 'system', 'description' => 'Manage system settings'],
            ['name' => 'manage_roles', 'display_name' => 'Manage Roles', 'category' => 'system', 'description' => 'Create and manage user roles'],
            ['name' => 'manage_permissions', 'display_name' => 'Manage Permissions', 'category' => 'system', 'description' => 'Manage user permissions'],

            // Listener Features
            ['name' => 'listener_access', 'display_name' => 'Listener Access', 'category' => 'listener', 'description' => 'Access listener features'],
            ['name' => 'create_playlists', 'display_name' => 'Create Playlists', 'category' => 'listener', 'description' => 'Create and manage playlists'],
            ['name' => 'like_music', 'display_name' => 'Like Music', 'category' => 'listener', 'description' => 'Like and favorite music'],
            ['name' => 'follow_artists', 'display_name' => 'Follow Artists', 'category' => 'listener', 'description' => 'Follow favorite artists'],
            ['name' => 'download_music', 'display_name' => 'Download Music', 'category' => 'listener', 'description' => 'Download music tracks'],

            // Artist Features
            ['name' => 'artist_access', 'display_name' => 'Artist Access', 'category' => 'artist', 'description' => 'Access artist features'],
        ];

        foreach ($permissions as $permission) {
            Permission::createIfNotExists($permission);
        }

        // Create Roles
        $roles = [
            [
                'name' => 'listener',
                'display_name' => 'Listener',
                'description' => 'Regular music listener with basic features',
                'color' => '#6366f1',
                'level' => 0,
                'is_default' => true,
            ],
            [
                'name' => 'artist',
                'display_name' => 'Artist',
                'description' => 'Music artist with upload and management capabilities',
                'color' => '#ec4899',
                'level' => 1,
            ],
            [
                'name' => 'admin',
                'display_name' => 'Administrator',
                'description' => 'Full system administrator with all permissions',
                'color' => '#8b5cf6',
                'level' => 5,
            ],
        ];

        foreach ($roles as $roleData) {
            $role = Role::firstOrCreate(['name' => $roleData['name']], $roleData);

            // Assign permissions based on role
            $this->assignPermissionsToRole($role);
        }

        // Update existing users with role_id
        $this->updateUserRoles();
    }

    private function assignPermissionsToRole(Role $role): void
    {
        $permissions = [];

        switch ($role->name) {
            case 'listener':
                $permissions = [
                    'listener_access',
                    'view_music',
                    'view_artists',
                    'view_blogs',
                    'create_playlists',
                    'like_music',
                    'follow_artists',
                    'download_music',
                ];
                break;

            case 'artist':
                $permissions = [
                    'artist_access',
                    'view_music',
                    'upload_music',
                    'edit_music',
                    'manage_albums',
                    'manage_artist_profile',
                    'view_artist_analytics',
                    'view_artists',
                    'view_blogs',
                    'create_playlists',
                    'like_music',
                    'follow_artists',
                ];
                break;

            case 'admin':
                // Admin gets all permissions
                $permissions = Permission::pluck('name')->toArray();
                break;
        }

        $permissionModels = Permission::whereIn('name', $permissions)->get();
        $role->permissions()->sync($permissionModels->pluck('id'));
    }

    private function updateUserRoles(): void
    {
        $users = User::all();
        
        foreach ($users as $user) {
            $role = Role::where('name', $user->role)->first();
            if ($role) {
                $user->update(['role_id' => $role->id]);
            }
        }
    }
}
