<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if notifications table already exists
        if (!Schema::hasTable('notifications')) {
            Schema::create('notifications', function (Blueprint $table) {
                $table->uuid('id')->primary();
                $table->string('type'); // Notification class name
                $table->morphs('notifiable'); // User or other model that receives notification
                $table->json('data'); // Notification data
                $table->timestamp('read_at')->nullable();
                $table->timestamps();
            });
        }

        // Add our custom columns to the existing notifications table
        Schema::table('notifications', function (Blueprint $table) {
            if (!Schema::hasColumn('notifications', 'priority')) {
                $table->string('priority')->default('normal')->after('read_at'); // low, normal, high, urgent
            }
            if (!Schema::hasColumn('notifications', 'category')) {
                $table->string('category')->nullable()->after('priority'); // music, system, social, etc.
            }
            if (!Schema::hasColumn('notifications', 'metadata')) {
                $table->json('metadata')->nullable()->after('category'); // Additional data
            }
            if (!Schema::hasColumn('notifications', 'expires_at')) {
                $table->timestamp('expires_at')->nullable()->after('metadata'); // When notification expires
            }
        });

        // Add indexes (Laravel will handle duplicates gracefully)
        try {
            Schema::table('notifications', function (Blueprint $table) {
                $table->index(['type', 'read_at'], 'notifications_type_read_at_idx');
                $table->index(['priority', 'created_at'], 'notifications_priority_created_at_idx');
                $table->index('expires_at', 'notifications_expires_at_idx');
            });
        } catch (\Exception $e) {
            // Indexes might already exist, continue
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notifications');
    }
};
