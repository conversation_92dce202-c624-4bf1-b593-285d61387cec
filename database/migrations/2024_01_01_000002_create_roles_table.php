<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('roles', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique(); // e.g., 'admin', 'artist', 'listener'
            $table->string('display_name'); // e.g., 'Administrator', 'Artist', 'Listener'
            $table->string('description')->nullable();
            $table->string('color')->default('#6366f1'); // Color for UI display
            $table->integer('level')->default(0); // Hierarchy level (higher = more permissions)
            $table->json('metadata')->nullable(); // Additional role data
            $table->boolean('is_active')->default(true);
            $table->boolean('is_default')->default(false); // Default role for new users
            $table->timestamps();

            $table->index(['is_active', 'is_default']);
            $table->index('level');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('roles');
    }
};
