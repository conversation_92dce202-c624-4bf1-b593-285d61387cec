<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add role_id foreign key (keeping the role enum for backward compatibility)
            $table->foreignId('role_id')->nullable()->after('role')->constrained()->onDelete('set null');
            
            // Add additional RBAC fields
            $table->boolean('is_active')->default(true)->after('role_id');
            $table->timestamp('last_login_at')->nullable()->after('is_active');
            $table->string('last_login_ip')->nullable()->after('last_login_at');
            $table->json('permissions_cache')->nullable()->after('last_login_ip'); // Cache user permissions
            $table->timestamp('permissions_updated_at')->nullable()->after('permissions_cache');
            
            // Add notification preferences
            $table->json('notification_preferences')->nullable()->after('permissions_updated_at');
            
            // Add security fields
            $table->boolean('force_password_change')->default(false)->after('notification_preferences');
            $table->timestamp('password_changed_at')->nullable()->after('force_password_change');
            $table->integer('failed_login_attempts')->default(0)->after('password_changed_at');
            $table->timestamp('locked_until')->nullable()->after('failed_login_attempts');

            // Add indexes
            $table->index(['role_id', 'is_active']);
            $table->index('last_login_at');
            $table->index('permissions_updated_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['role_id']);
            $table->dropColumn([
                'role_id',
                'is_active',
                'last_login_at',
                'last_login_ip',
                'permissions_cache',
                'permissions_updated_at',
                'notification_preferences',
                'force_password_change',
                'password_changed_at',
                'failed_login_attempts',
                'locked_until'
            ]);
        });
    }
};
