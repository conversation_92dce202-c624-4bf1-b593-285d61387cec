{"private": true, "type": "module", "scripts": {"build": "vite build && vite build --ssr", "dev": "vite"}, "devDependencies": {"@prettier/plugin-php": "^0.22.4", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/lodash": "^4.17.15", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/react-responsive-masonry": "^2.6.0", "@vitejs/plugin-react": "^4.3.4", "@vue/server-renderer": "^3.3.13", "autoprefixer": "^10.4.20", "axios": "^1.7.9", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.5.1", "prettier": "^3.4.2", "tailwindcss": "^3.4.17", "typescript": "^5.7.3", "vite": "^5.4.14"}, "dependencies": {"@headlessui/react": "^2.2.0", "@inertiajs/react": "^1.3.0", "@inertiajs/server": "^0.1.0", "chart.js": "^4.4.9", "classnames": "^2.5.1", "framer-motion": "^12.0.6", "lodash": "^4.17.21", "lucide-react": "^0.469.0", "moment": "^2.30.1", "prop-types": "^15.8.1", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.2", "react-infinite-scroll-component": "^6.1.0", "react-markdown": "^10.1.0", "react-responsive-masonry": "^2.7.1", "recharts": "^2.15.2", "sweetalert2": "^11.17.2", "ziggy-js": "^2.5.0"}, "prettier": {"semi": true, "singleQuote": true, "useTabs": false, "tabWidth": 2, "trailingComma": "all", "printWidth": 80, "arrowParens": "avoid"}}